apiVersion: apps/v1
kind: Deployment
metadata:
  name: PLACEHOLDER_PROJECT_ID
  labels:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 2
  selector:
    matchLabels:
      app: PLACEHOLDER_PROJECT_ID
  template:
    metadata:
      labels:
        app: PLACEHOLDER_PROJECT_ID
        app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
        app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
        app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
        app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    spec:
      containers:
      - name: PLACEHOLDER_PROJECT_ID
        image: PLACEH<PERSON>DER_DOCKER_IMAGE
        imagePullPolicy: Always
        ports:
        - containerPort: PLACEHOLDER_CONTAINER_PORT
          name: http
        envFrom:
        - configMapRef:
            name: PLACEHOLDER_PROJECT_ID-config
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: JWT_SECRET
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: SESSION_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_SSL_MODE
        {{#eq APPLICATION_TYPE "springboot-backend"}}
        # Spring Boot specific database configuration
        - name: SPRING_DATASOURCE_URL
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: SPRING_DATASOURCE_URL
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_PASSWORD
        - name: SPRING_DATASOURCE_DRIVER_CLASS_NAME
          value: "org.postgresql.Driver"
        {{/eq}}
        {{#eq APPLICATION_TYPE "django-backend"}}
        # Django specific database configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DATABASE_URL
        - name: DJANGO_SETTINGS_MODULE
          valueFrom:
            configMapKeyRef:
              name: PLACEHOLDER_PROJECT_ID-config
              key: DJANGO_SETTINGS_MODULE
        {{/eq}}
        {{#eq APPLICATION_TYPE "nest-backend"}}
        # NestJS/TypeORM specific database configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DATABASE_URL
        - name: TYPEORM_CONNECTION
          value: "postgres"
        - name: TYPEORM_HOST
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_HOST
        - name: TYPEORM_PORT
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_PORT
        - name: TYPEORM_USERNAME
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_USER
        - name: TYPEORM_PASSWORD
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_PASSWORD
        - name: TYPEORM_DATABASE
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_NAME
        {{/eq}}
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: GOOGLE_CLIENT_SECRET
        {{#eq APPLICATION_TYPE "springboot-backend"}}
        # Spring Boot Health Checks
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 120
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        {{/eq}}
        {{#eq APPLICATION_TYPE "django-backend"}}
        # Django Health Checks
        livenessProbe:
          httpGet:
            path: /health/
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health/
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 9
        {{/eq}}
        {{#eq APPLICATION_TYPE "nest-backend"}}
        # NestJS Health Checks
        livenessProbe:
          httpGet:
            path: /health
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 45
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        {{/eq}}
        {{#eq APPLICATION_TYPE "react-frontend"}}
        # React Frontend Health Checks (TCP for static content)
        livenessProbe:
          tcpSocket:
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: PLACEHOLDER_CONTAINER_PORT
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        {{/eq}}
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
