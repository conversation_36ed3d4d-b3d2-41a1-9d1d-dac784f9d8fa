apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-test-config
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: "d60cac31"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "ai-spring-backend-test"
  PROJECT_ID: "ai-spring-backend-test"
  APPLICATION_TYPE: "springboot-backend"
  SOURCE_REPO: "ChidhagniConsulting/gitops-argocd-apps"
  SOURCE_BRANCH: "main"
  COMMIT_SHA: "d60cac31"
  NODE_ENV: "staging"
  PORT: "8092"
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:8092"
  API_URL: "http://localhost:8092/api"
  
  # Common Backend Configuration
  SERVER_PORT: "8092"

  
  # Spring Boot Configuration
  SPRING_PROFILES_ACTIVE: "staging"
  SPRING_APPLICATION_NAME: "ai-spring-backend-test"

  # Spring Boot Database Configuration (Managed DigitalOcean Database)

  # SPRING_DATASOURCE_USERNAME is configured via environment variables in deployment
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_JPA_SHOW_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_TIME_ZONE: "UTC"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when_authorized"
  MANAGEMENT_HEALTH_DEFAULTS_ENABLED: "true"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  MANAGEMENT_HEALTH_MAIL_ENABLED: "false"
  MANAGEMENT_ENDPOINT_HEALTH_PROBES_ADD_ADDITIONAL_PATHS: "true"
  MANAGEMENT_HEALTH_LIVENESSSTATE_ENABLED: "true"
  MANAGEMENT_HEALTH_READINESSSTATE_ENABLED: "true"

  # JVM Configuration - Optimized for staging (4Gi container limit)
  JAVA_OPTS: "-Xms1g -Xmx3g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"
  

  

  

  
