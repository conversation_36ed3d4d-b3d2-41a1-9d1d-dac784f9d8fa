apiVersion: v1
kind: ConfigMap
metadata:
  name: PLACEH<PERSON>DER_PROJECT_ID-config
  labels:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "PLACEHOLDER_APP_NAME"
  PROJECT_ID: "PLACEHOLDER_PROJECT_ID"
  APPLICATION_TYPE: "PLACEHOLDER_APPLICATION_TYPE"
  SOURCE_REPO: "PLACEHOLDER_SOURCE_REPO"
  SOURCE_BRANCH: "PLACEHOLDER_SOURCE_BRANCH"
  COMMIT_SHA: PLACEH<PERSON>DER_COMMIT_SHA
 
  NODE_ENV: "staging"
  PORT: "PLACEHOLDER_CONTAINER_PORT"
 
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:PLACEHOLDER_CONTAINER_PORT"
  API_URL: "http://localhost:PLACEHOLDER_CONTAINER_PORT/api"
  
  # Common Backend Configuration
  SERVER_PORT: "PLACEHOLDER_CONTAINER_PORT"

  {{#eq APPLICATION_TYPE "springboot-backend"}}
  # Spring Boot Configuration
  SPRING_PROFILES_ACTIVE: "staging"
  SPRING_APPLICATION_NAME: "PLACEHOLDER_PROJECT_ID"

  # Spring Boot Database Configuration (Managed DigitalOcean Database)

  # SPRING_DATASOURCE_USERNAME is configured via environment variables in deployment
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_JPA_SHOW_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_TIME_ZONE: "UTC"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when_authorized"
  MANAGEMENT_HEALTH_DEFAULTS_ENABLED: "true"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  MANAGEMENT_HEALTH_MAIL_ENABLED: "false"
  MANAGEMENT_ENDPOINT_HEALTH_PROBES_ADD_ADDITIONAL_PATHS: "true"
  MANAGEMENT_HEALTH_LIVENESSSTATE_ENABLED: "true"
  MANAGEMENT_HEALTH_READINESSSTATE_ENABLED: "true"

  # JVM Configuration - Optimized for staging (4Gi container limit)
  JAVA_OPTS: "-Xms1g -Xmx3g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"
  {{/eq}}

  {{#eq APPLICATION_TYPE "django-backend"}}
  # Django Configuration
  DJANGO_SETTINGS_MODULE: "app.settings.staging"
  DEBUG: "False"
  ALLOWED_HOSTS: "localhost,127.0.0.1"

  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"

  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_ALLOW_CREDENTIALS: "true"

  # Django Database Configuration
  DATABASE_ENGINE: "django.db.backends.postgresql"
  DATABASE_CONN_MAX_AGE: "600"
  DATABASE_CONN_HEALTH_CHECKS: "True"
  DATABASE_OPTIONS_CONNECT_TIMEOUT: "30"
  DATABASE_OPTIONS_COMMAND_TIMEOUT: "60"
  DATABASE_OPTIONS_SERVER_SIDE_BINDING: "True"
  {{/eq}}

  {{#eq APPLICATION_TYPE "nest-backend"}}

  # NestJS CORS Configuration
  CORS_ORIGIN: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_CREDENTIALS: "true"

  # TypeORM Configuration
  TYPEORM_SYNCHRONIZE: "false"
  TYPEORM_LOGGING: "false"
  TYPEORM_ENTITIES: "dist/**/*.entity.js"
  TYPEORM_MIGRATIONS: "dist/migrations/*.js"
  TYPEORM_MIGRATIONS_DIR: "src/migrations"
  TYPEORM_MAX_QUERY_EXECUTION_TIME: "30000"
  TYPEORM_POOL_SIZE: "10"
  TYPEORM_CONNECTION_TIMEOUT: "30000"
  TYPEORM_ACQUIRE_TIMEOUT: "60000"
  TYPEORM_TIMEOUT: "60000"
  {{/eq}}

  {{#eq APPLICATION_TYPE "react-frontend"}}
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:PLACEHOLDER_CONTAINER_PORT/api"
  REACT_APP_ENVIRONMENT: "staging"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "false"
  {{/eq}}
